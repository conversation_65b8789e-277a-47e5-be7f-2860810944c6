using System.Collections.Generic;
using UnityEngine;
using FairyGUI;

/// <summary>
/// 简化的操作层接口，封装复杂的层级管理逻辑
/// </summary>
public class SimplifiedOperationLayer
{
    private SmartLayerManager layerManager;
    private GridPositionManager gridManager;
    private StackingManager stackingManager;
    private OperationLayer originalOperationLayer;
    private JigsawPanel parentPanel;
    private GComponent rootContainer;
    
    // 维护性能优化
    private float lastMaintenanceTime = 0f;
    private const float MAINTENANCE_INTERVAL = 10f; // 10秒执行一次维护
    
    public SimplifiedOperationLayer(OperationLayer operationLayer, JigsawPanel panel, GComponent container)
    {
        originalOperationLayer = operationLayer;
        parentPanel = panel;
        rootContainer = container;
        
        // 初始化管理器
        layerManager = new SmartLayerManager(container, panel);
        stackingManager = new StackingManager(panel);
        
        // 从SmartLayerManager获取GridPositionManager的引用
        gridManager = layerManager.GetGridManager();
    }
    
    /// <summary>
    /// 拼块拖拽开始事件
    /// </summary>
    /// <param name="piece">拼块</param>
    public void OnPieceDragStart(JigsawPiece piece)
    {
        if (piece == null || piece.isDisposed || layerManager == null) return;
        
        try
        {
            // 注册拼块到层级管理系统（如果还未注册）
            Vector2Int gridPos = CalculateGridPosition(piece);
            layerManager.RegisterPiece(piece, gridPos);
            
            // 开始拖拽
            layerManager.StartDragging(piece);
        }
        catch (System.Exception ex)
        {
            UnityEngine.Debug.LogError($"OnPieceDragStart failed for piece {piece.pieceIndex}: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 拼块拖拽结束事件
    /// </summary>
    /// <param name="piece">拼块</param>
    public void OnPieceDragEnd(JigsawPiece piece)
    {
        if (piece == null || piece.isDisposed || layerManager == null) return;
        
        try
        {
            // 停止拖拽
            layerManager.StopDragging(piece);
            
            // 检查并处理叠加情况
            HandleStackingAfterDrop(piece);
            
            // 触发定期维护检查
            CheckAndPerformMaintenance();
        }
        catch (System.Exception ex)
        {
            UnityEngine.Debug.LogError($"OnPieceDragEnd failed for piece {piece.pieceIndex}: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 拼块位置变化事件
    /// </summary>
    /// <param name="piece">拼块</param>
    public void OnPiecePositionChanged(JigsawPiece piece)
    {
        if (piece == null || piece.isDisposed || layerManager == null) return;
        
        try
        {
            // 更新层级管理和thickness位置
            layerManager.UpdatePiecePosition(piece);
            // UpdatePiecePosition中已经包含了thickness位置更新
            layerManager.UpdatePieceLayer(piece);
        }
        catch (System.Exception ex)
        {
            UnityEngine.Debug.LogError($"OnPiecePositionChanged failed for piece {piece.pieceIndex}: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 添加拼块到层级管理
    /// </summary>
    /// <param name="piece">拼块</param>
    public void AddPieceToLayer(JigsawPiece piece)
    {
        if (piece == null) return;
        
        Vector2Int gridPos = CalculateGridPosition(piece);
        layerManager.RegisterPiece(piece, gridPos);
    }
    
    /// <summary>
    /// 从层级管理移除拼块
    /// </summary>
    /// <param name="piece">拼块</param>
    public void RemovePieceFromLayer(JigsawPiece piece)
    {
        if (piece == null) return;
        
        layerManager.UnregisterPiece(piece);
    }
    
    /// <summary>
    /// 更新拼块thickness位置
    /// </summary>
    /// <param name="piece">拼块</param>
    public void UpdatePieceThicknessPosition(JigsawPiece piece)
    {
        if (piece == null) return;
        
        // 直接调用位置变化事件，确保一致性
        OnPiecePositionChanged(piece);
    }
    
    /// <summary>
    /// 处理拖拽结束后的叠加逻辑
    /// </summary>
    /// <param name="piece">拼块</param>
    private void HandleStackingAfterDrop(JigsawPiece piece)
    {
        Vector2Int gridPos = CalculateGridPosition(piece);
        
        // 获取该网格位置的所有拼块
        var piecesAtGrid = GetPiecesAtGrid(gridPos);
        
        if (piecesAtGrid.Count > 1)
        {
            // 有多个拼块在同一位置，处理叠加
            var otherPieces = new List<JigsawPiece>(piecesAtGrid);
            otherPieces.Remove(piece);
            
            stackingManager.HandleOverlapConflict(piece, otherPieces);
        }
    }
    
    /// <summary>
    /// 获取指定网格位置的所有拼块
    /// </summary>
    /// <param name="gridPos">网格位置</param>
    /// <returns>拼块列表</returns>
    private List<JigsawPiece> GetPiecesAtGrid(Vector2Int gridPos)
    {
        // 这里需要从层级管理系统获取数据
        // 临时实现，需要完善
        var pieces = new List<JigsawPiece>();
        
        if (parentPanel != null)
        {
            var allPieces = parentPanel.GetPiecesInOperationLayer();
            foreach (var piece in allPieces)
            {
                if (CalculateGridPosition(piece) == gridPos)
                {
                    pieces.Add(piece);
                }
            }
        }
        
        return pieces;
    }
    
    /// <summary>
    /// 计算拼块的网格位置
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <returns>网格位置</returns>
    private Vector2Int CalculateGridPosition(JigsawPiece piece)
    {
        if (piece == null || parentPanel == null) return Vector2Int.zero;
        
        Vector2 centerOffset = new Vector2(piece.width * 0.5f, piece.height * 0.5f);
        Vector2 globalCenterPos = piece.LocalToGlobal(centerOffset);
        Vector2 operationLayerLocalPos = parentPanel.GlobalToOperationLayerLocal(globalCenterPos);
        
        return parentPanel.GetGridPosition(operationLayerLocalPos);
    }
    
    /// <summary>
    /// 检查并执行定期维护
    /// </summary>
    private void CheckAndPerformMaintenance()
    {
        float currentTime = Time.time;
        if (currentTime - lastMaintenanceTime > MAINTENANCE_INTERVAL)
        {
            PerformMaintenance();
            lastMaintenanceTime = currentTime;
        }
    }
    
    /// <summary>
    /// 执行定期维护
    /// </summary>
    public void PerformMaintenance()
    {
        if (layerManager != null)
        {
            // 压缩层级索引避免溢出
            layerManager.OptimizeLayerIndices();
        }
        
        // 清理无效的引用或数据
        CleanupInvalidReferences();
    }
    
    /// <summary>
    /// 清理无效引用
    /// </summary>
    private void CleanupInvalidReferences()
    {
        // 这里可以添加清理逻辑，比如移除已被销毁的拼块引用等
    }
    
    /// <summary>
    /// 获取层级管理器（用于高级操作）
    /// </summary>
    /// <returns>智能层级管理器</returns>
    public SmartLayerManager GetLayerManager()
    {
        return layerManager;
    }
    
    /// <summary>
    /// 获取叠加管理器（用于高级操作）
    /// </summary>
    /// <returns>叠加管理器</returns>
    public StackingManager GetStackingManager()
    {
        return stackingManager;
    }
    
    /// <summary>
    /// 重新整理所有拼块的层级
    /// </summary>
    public void ReorganizeAllLayers()
    {
        if (layerManager != null)
        {
            layerManager.OptimizeLayerIndices();
        }
    }
    
    /// <summary>
    /// 清空所有层级管理数据
    /// </summary>
    public void Clear()
    {
        if (layerManager != null)
        {
            layerManager.Clear();
        }
    }
    
    /// <summary>
    /// 将拼块thickness移动到拖拽层（新系统中自动处理）
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <param name="dragLayer">拖拽层</param>
    public void MovePieceThicknessToDragLayer(JigsawPiece piece, GComponent dragLayer)
    {
        // 新系统中拖拽状态通过层级自动处理
        OnPieceDragStart(piece);
    }
    
    /// <summary>
    /// 将拼块thickness从拖拽层移回操作层（新系统中自动处理）
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <param name="dragLayer">拖拽层</param>
    public void MovePieceThicknessBackToLayer(JigsawPiece piece, GComponent dragLayer)
    {
        // 新系统中拖拽结束状态通过层级自动处理
        OnPieceDragEnd(piece);
    }
}